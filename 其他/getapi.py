from flask import Flask, request
import json
import os
from datetime import datetime

app = Flask(__name__)

# 保存API脚本的目录
API_SCRIPT_DIR = "api_scripts"
os.makedirs(API_SCRIPT_DIR, exist_ok=True)

def save_api_script(method, url, params, headers):
    # 生成脚本内容
    script = f"""import requests

url = "{url}"
headers = {json.dumps(headers, indent=4)}
params = {json.dumps(params, indent=4)}

response = requests.{method.lower()}(url, headers=headers, params=params)
print(response.text)
"""
    # 以时间戳命名
    filename = f"{API_SCRIPT_DIR}/api_{datetime.now().strftime('%Y%m%d_%H%M%S')}.py"
    with open(filename, "w", encoding="utf-8") as f:
        f.write(script)
    print(f"API脚本已保存到: {filename}")

@app.route('/', defaults={'path': ''}, methods=['GET', 'POST', 'PUT', 'DELETE', 'PATCH'])
@app.route('/<path:path>', methods=['GET', 'POST', 'PUT', 'DELETE', 'PATCH'])
def catch_all(path):
    method = request.method
    url = request.url
    headers = dict(request.headers)
    if method == "GET":
        params = request.args.to_dict()
    else:
        try:
            params = request.get_json(force=True)
        except:
            params = request.form.to_dict()
    save_api_script(method, url, params, headers)
    return {"msg": "已捕获请求并生成API脚本"}, 200

if __name__ == '__main__':
    app.run(port=5000, debug=True)
