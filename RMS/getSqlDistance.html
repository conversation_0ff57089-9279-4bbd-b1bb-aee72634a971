<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>生成MySQL getDistance查询</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            background-color: #f0f0f0;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
            max-width: 400px;
            width: 100%;
        }
        h2 {
            text-align: center;
            margin-bottom: 20px;
        }
        .input-row {
            display: flex;
            justify-content: space-between;
            margin-top: 20px;
        }
        label {
            flex: 1;
            margin-right: 10px;
            font-size: 14px;
        }
        input {
            padding: 10px;
            width: calc(50% - 10px);
            box-sizing: border-box;
            border: 1px solid #ccc;
            border-radius: 5px;
            font-size: 14px;
        }
        button {
            margin-top: 20px;
            padding: 10px;
            width: 100%;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #45a049;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            background-color: #f1f1f1;
            border: 1px solid #ddd;
            border-radius: 5px;
            text-align: center;
        }
        .result code {
            display: block;
            background-color: #eee;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
        }
    </style>
</head>
<body>

<div class="container">
    <h2>生成MySQL查询语句并计算距离</h2>

    <form id="sqlForm">
        <div class="input-row">
            <label for="lng1">酒店 经度(lng):</label>
            <input type="number" id="lng1" step="any" placeholder="例如: 116.4074" required>
        </div>

        <div class="input-row">
            <label for="lat1">酒店 纬度(lat):</label>
            <input type="number" id="lat1" step="any" placeholder="例如: 39.9042" required>
        </div>

        <div class="input-row">
            <label for="lng2">场馆 经度(lng):</label>
            <input type="number" id="lng2" step="any" placeholder="例如: 121.4737" required>
        </div>

        <div class="input-row">
            <label for="lat2">场馆 纬度(lat):</label>
            <input type="number" id="lat2" step="any" placeholder="例如: 31.2304" required>
        </div>

        <button type="button" onclick="generateSQL()">生成SQL语句</button>
    </form>

    <div class="result" id="result">
        生成的SQL语句将显示在这里。
    </div>
    <div class="result" id="distanceResult">
        计算的距离将显示在这里。
    </div>
</div>

<script>
    function generateSQL() {
        // 获取地标1和地标2的经纬度值
        const lng1 = parseFloat(document.getElementById('lng1').value);
        const lat1 = parseFloat(document.getElementById('lat1').value);
        const lng2 = parseFloat(document.getElementById('lng2').value);
        const lat2 = parseFloat(document.getElementById('lat2').value);

        // 生成MySQL查询语句
        const sql = `SELECT ST_Distance_Sphere(POINT(${lng1}, ${lat1}), POINT(${lng2}, ${lat2})) AS distance;`;

        // 显示生成的SQL语句
        document.getElementById('result').innerHTML = `<code>${sql}</code>`;

        // 计算两点之间的距离（米）
        const distance = calculateDistance(lng1, lat1, lng2, lat2);
        document.getElementById('distanceResult').innerHTML = `计算的距离是 ${distance} 米`;
    }

    function calculateDistance(lng1, lat1, lng2, lat2) {
        const R = 6370986.984; // 调整后的地球半径（米）
        const toRadians = degrees => degrees * (Math.PI / 180);

        const dLat = toRadians(lat2 - lat1);
        const dLng = toRadians(lng2 - lng1);

        const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
                  Math.cos(toRadians(lat1)) * Math.cos(toRadians(lat2)) *
                  Math.sin(dLng / 2) * Math.sin(dLng / 2);
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

        return R * c; // 返回距离（米），不进行四舍五入
    }
</script>

</body>
</html>
