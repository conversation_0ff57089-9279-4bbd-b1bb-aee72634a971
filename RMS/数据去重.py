import json
import pandas as pd
from RMS.distinct import json_data

# 假设你有一个包含 JSON 数据的文件 data.json
json_data = json_data

# 提取 location 字段
locations = [item['location'] for item in json_data['list']]

# 创建 DataFrame
df = pd.DataFrame(locations, columns=['location'])

# 去重并按 ASCII 码顺序排序
distinct_sorted_locations = df['location'].drop_duplicates().sort_values()

# 打印结果，添加序号
print("去重并按 ASCII 码排序后的 locations:")
for index, location in enumerate(distinct_sorted_locations, start=1):
    print(f"{index}. {location}")