import json
from RMS.distinct import json_data1
# 解析JSON数据
json_data = json_data1

# 提取所有的distance值，并确保转换为浮点数，跳过空字符串
distances = [float(item['distance']) for item in json_data['list'] if item['distance'] not in [None, '']]

# 统计小于1000的distance值的个数
count_small_distances = sum(1 for distance in distances if distance < 10000)
# count_small_distances = sum(1 for distance in distances if distance > 10000)

# 输出结果
print(f"小于1000的distance值的个数为: {count_small_distances}")