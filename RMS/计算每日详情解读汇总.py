from ctypes.wintypes import DOUBLE
from openpyxl.styles.numbers import FORMAT_NUMBER
# 计算wow=(avg_7/avg_14)-1，相当于最近7天的日均浏览量周环比增幅
avg_7 = 4561
avg_14 = 2712.2857142857142
# 四舍五入保留一位小数
wow = round(((avg_7/avg_14 -1) * 100), 1)
# -------------------------------------------------
# occ_yoy = 'occ_thisyear_forecast / occ_lastyear -1'
# lastyear: Txb Tb
# 预定热度
occ_thisyear_forecast = 7.493646621704102
occ_lastyear = 27.38
occ_yoy = (occ_thisyear_forecast / occ_lastyear -1) * 100
# -------------------------------------------------------
# 预定进度
occ_thisyear_forecast = 9.8 # 本酒店预估出租率
occ_current = 7.1 # 本酒店当前出租率

# 计算 bookingpace
# bookingpace = (occ_thisyear_forecast - occ_current) / 100
bookingpace = 0.037
roomCount = 422
# 计算 LCL=FORMAT_NUMBER( bookingpace - 0.05, '0%'),无需格式化，OCC已经格式化了
LCL  = (bookingpace - 0.05) * roomCount

# 计算 LCL=FORMAT_NUMBER( bookingpace - 0.05, '0%'),无需格式化，OCC已经格式化了
# 在 Python 中使用 min 函数来实现 LEAST 的效果，并限制在 [0, 1] 范围内
UCL = (min(bookingpace + 0.05, 1)) * roomCount

print("bookingpace: ", bookingpace)
print("lcl: ", LCL)
print("ucl: ", UCL)
# print('occ_current: ', occ_current)
# print('occ_thisyear_forecast: ', occ_thisyear_forecast)
# print('occ_yoy: ', occ_yoy)
# print('wow',wow)
