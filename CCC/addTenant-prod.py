"""
===========================
Name:dkzeng
Exmail:<EMAIL>
Time:2025/5/28 16:43
company：华客信息科技有限公司
===========================
"""
import time
import requests
from jsonpath import jsonpath

def get_auth_token():
    admin = input('请输入账号：')
    password = input('请输入密码：')

    # base_url = 'https://adminservice-fat.vocustcloud.com'
    base_url = 'https://adminservice.vocustcloud.com'
    login_url = f'{base_url}/api/login?__={int(time.time() * 1000)}'
    response = requests.post(
        login_url,
        json={"username": f"{admin}", "password": f"{password}"},
        headers={'Content-Type': 'application/json'}
    ).json()
    return jsonpath(response, '$..token')[0]

def run_main_flow(token):
    # 租户信息输入
    companyname = input('请输入租户名称：')
    contact = input('请输入租户账号：')
    city = input('请输入城市：')
    # base_url = 'https://adminservice-fat.vocustcloud.com'
    base_url = 'https://adminservice.vocustcloud.com'
    headers = {
        'Content-Type': 'application/json',
        'token': token
    }

    # 新建租户
    url_add_tenant = f'{base_url}/api/addTenantinfo?__={int(time.time() * 1000)}'
    data_add_tenant = {
        "companyname": companyname, #  租户名称
        "contact": contact, # 租户账号
        "password": '123456', #  密码
        "startTime": time.strftime("%Y-%m-%d", time.localtime()),
        "endTime": '2099-12-31',
        "modules": [{"moduleId": 10, "functionIds": [2]}], # 可选应用
        "city": city, #  城市
        "property": 0,# 属性（正式）
        "customerSuccess": '丁峥嵘',# 客成
        "salesman": '丁峥嵘',#  销售
        "maxAgentCount": 20,# 最大客服数
        "hardPhoneStatus": 1,#  硬电话
        "ttsId": 21,# TTS供应商，讯飞
        "industryid": 6,# 行业（酒店）
        "overflow": 2,# 溢出租户（否）
        "overflowPuids": []#  溢出租户puids
    }
    result_add_tenant = requests.post(url=url_add_tenant, headers=headers, json=data_add_tenant).json()
    print("新建租户结果：","\n", result_add_tenant,"\n")
    ack = jsonpath(result_add_tenant, '$..ack')[0]
    # 判断是否执行成功
    if ack == "Success":
        # 查询租户并提取 puid
        url_query = f'{base_url}/api/queryTenantinfos?__={int(time.time() * 1000)}'
        data_query = {"companyname": "", "contact": "", "pageSize": 10, "pageNo": 1, "status": ""}
        result_query = requests.post(url=url_query, headers=headers, json=data_query).json()
        puid = jsonpath(result_query, '$..puid')[0]

        # 添加菜单
        url_save_menu = f'{base_url}/api/saveTenantSources?__={int(time.time() * 1000)}'
        data_save_menu = {
            "puid": f"{puid}",
            "agentSourcesIds": [5, 52, 146, 2, 53, 54, 148],
            "adminSourcesIds": [5, 52, 64, 146, 9, 73, 96, 149, 1, 2, 53, 54, 66, 148, 8, 114, 115, 116, 74, 76, 78, 79, 80, 81, 83, 84, 86, 87, 101, 21, 22, 23, 134, 143, 130]
        }
        result_save_menu = requests.post(url=url_save_menu, headers=headers, json=data_save_menu).json()
        print("添加菜单结果：", "\n",result_save_menu,"\n")
    # 否则重新运行新建租户
    else:
        run_main_flow(token)

# 主程序逻辑
token = get_auth_token()

while True:
    run_main_flow(token)

    while True:
        choice = input("\n  1.继续创建租户\n  2.退出程序\n")
        if choice == '1':
            break  # 跳出内层循环，重新运行流程
        elif choice == '2':
            print("退出程序。")
            exit()  # 直接退出
        else:
            print("\n  输入无效，请重新输入！")

