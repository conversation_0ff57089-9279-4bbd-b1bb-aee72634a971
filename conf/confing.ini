[logging]
name=mylog
level=DEBUG
filename=cases_log.log
fh_level=DEBUG
sh_level=DEBUG

[env]
base_url = https://robot-data-uat.cemsmart.com/admin/robot/send
headers = {"Authorization":"Basic eG06eG1fc2VjcmV0","Content-Type":"application/json","Robotid":"0000001","Token":"be87779a78fa46a28dd54b8c6539ebe7"}
;headers = {"Authorization":"Basic eG06eG1fc2VjcmV0","Content-Type":"application/json","Robotid":"0000013","Token":"c91f32f2fc6e40d8af9beb2bf2c7193e"}
;headers = {"Authorization":"Basic eG06eG1fc2VjcmV0","Content-Type":"application/json","Robotid":"0000052","Token":"6725471f47fe4146872fe3f9d41e03c8"}
;headers = {'Content-Type': 'text/plain'}
;base_url =https://robot-data.vocust.com/robot/robot/send


[confs]
;上传文件到企业微信服务器的URL
upload_url = "https://qyapi.weixin.qq.com/cgi-bin/webhook/upload_media?key=25bae3f1-9dcf-43c8-a374-094d29f0ac28&type=file"
;webhook 地址
send_url = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=25bae3f1-9dcf-43c8-a374-094d29f0ac28"
;文件路径
file_path = r"\python_project_appium\addPhone\addPhone.html"
;@对应群中的人员
mentioned_mobile_list = ["18307090266"]

headers = {'Content-Type': 'application/json'}
[test_data]


[mysql]
